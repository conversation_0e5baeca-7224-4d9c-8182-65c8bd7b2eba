import React, { useState, useRef, useEffect } from 'react';
import {
  User,
  ChevronDown,
  LogIn,
  UserCheck,
  X,
  Trash2,
  AlertCircle,
  Loader,
  Users,
  ArrowLeft,
  Eye,
  EyeOff,
  HelpCircle,
  Check
} from 'lucide-react';
import { useAuth } from '../AuthContext';

const AdminSwitcher = () => {
  const {
    adminProfile,
    rememberedAdmins,
    currentAdminId,
    isAdminSwitching,
    authenticateAdminSwitch,
    switchToRememberedAdmin,
    returnToPrimaryAdmin,
    removeRememberedAdmin,
    error
  } = useAuth();

  const [isOpen, setIsOpen] = useState(false);
  const [showLoginForm, setShowLoginForm] = useState(false);
  const [loginEmail, setLoginEmail] = useState('');
  const [loginPassword, setLoginPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loginError, setLoginError] = useState('');
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [showRemoveConfirm, setShowRemoveConfirm] = useState(null);
  const [successMessage, setSuccessMessage] = useState('');
  const [showHelp, setShowHelp] = useState(false);

  const dropdownRef = useRef(null);
  const switcherRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        isOpen &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target) &&
        switcherRef.current &&
        !switcherRef.current.contains(event.target)
      ) {
        setIsOpen(false);
        setShowLoginForm(false);
        setShowRemoveConfirm(null);
        setShowHelp(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen]);

  // Reset form when dropdown closes
  useEffect(() => {
    if (!isOpen) {
      setShowLoginForm(false);
      setLoginEmail('');
      setLoginPassword('');
      setLoginError('');
      setShowPassword(false);
      setShowRemoveConfirm(null);
      setShowHelp(false);
    }
  }, [isOpen]);

  const handleToggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleNewAdminLogin = async (e) => {
    e.preventDefault();
    if (!loginEmail.trim() || !loginPassword.trim()) {
      setLoginError('Please enter both email and password');
      return;
    }

    // Check if trying to login to the same account
    // Only prevent if the admin is currently active AND still in remembered admins
    const isCurrentlyActiveAdmin = currentAdmin &&
      loginEmail.trim().toLowerCase() === currentAdmin.email.toLowerCase() &&
      rememberedAdmins.some(admin => admin.email.toLowerCase() === loginEmail.trim().toLowerCase());

    if (isCurrentlyActiveAdmin) {
      setLoginError('You are already logged into this account');
      return;
    }

    setIsLoggingIn(true);
    setLoginError('');

    try {
      const result = await authenticateAdminSwitch(loginEmail.trim(), loginPassword);

      if (result.success) {
        // Success - show brief success message and close
        setSuccessMessage(`Switched to ${result.admin.first_name} ${result.admin.last_name}`);
        setTimeout(() => {
          setIsOpen(false);
          setShowLoginForm(false);
          setLoginEmail('');
          setLoginPassword('');
          setLoginError('');
          setSuccessMessage('');
        }, 1500);
      } else {
        setLoginError(result.message || 'Authentication failed');
      }
    } catch (error) {
      setLoginError('Network error. Please try again.');
    } finally {
      setIsLoggingIn(false);
    }
  };

  const handleQuickSwitch = async (adminId) => {
    if (isAdminSwitching) return;

    // Prevent switching to the same admin
    if (currentAdminId === adminId) {
      setLoginError('You are already logged into this account');
      setTimeout(() => setLoginError(''), 3000);
      return;
    }

    try {
      const result = await switchToRememberedAdmin(adminId);
      if (result.success) {
        setSuccessMessage(`Switched to ${result.admin.first_name} ${result.admin.last_name}`);
        setTimeout(() => {
          setIsOpen(false);
          setSuccessMessage('');
        }, 1500);
      }
    } catch (error) {
      console.error('Quick switch failed:', error);
    }
  };

  const handleReturnToPrimary = async () => {
    if (isAdminSwitching) return;

    try {
      const result = await returnToPrimaryAdmin();
      if (result.success) {
        setSuccessMessage('Returned to primary account');
        setTimeout(() => {
          setIsOpen(false);
          setSuccessMessage('');
        }, 1500);
      }
    } catch (error) {
      console.error('Return to primary failed:', error);
    }
  };

  const handleRemoveAdmin = async (adminId, e) => {
    e.stopPropagation();
    
    if (showRemoveConfirm === adminId) {
      // Confirm removal
      removeRememberedAdmin(adminId);
      setShowRemoveConfirm(null);
    } else {
      // Show confirmation
      setShowRemoveConfirm(adminId);
    }
  };

  const getCurrentAdminInfo = () => {
    if (!adminProfile) return null;
    
    const isCurrentSwitched = currentAdminId && currentAdminId !== adminProfile.id;
    return {
      ...adminProfile,
      isSwitched: isCurrentSwitched
    };
  };

  const currentAdmin = getCurrentAdminInfo();

  return (
    <div className="relative" ref={switcherRef}>
      {/* Main Switcher Button */}
      <button
        onClick={handleToggleDropdown}
        className={`flex items-center pl-3 pr-1 py-2 rounded-lg transition-colors ${
          currentAdminId
            ? 'bg-orange-50 text-orange-700 border border-orange-200 hover:bg-orange-100'
            : 'bg-blue-50 text-blue-700 border border-blue-200 hover:bg-blue-100'
        }`}
        aria-label="Admin account switcher"
        disabled={isAdminSwitching}
        style={{ minWidth: '200px' }}
      >
        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
          currentAdminId ? 'bg-orange-100' : 'bg-indigo-100'
        }`}>
          {currentAdminId ? (
            <Users className="w-4 h-4 text-orange-700" />
          ) : (
            <User className="w-4 h-4 text-indigo-700" />
          )}
        </div>

        <div className="hidden sm:block text-left ml-2 flex-1">
          <div className="text-sm font-medium">
            {currentAdmin ? `${currentAdmin.first_name} ${currentAdmin.last_name}` : 'Loading...'}
          </div>
        </div>

        {isAdminSwitching && (
          <Loader className="w-4 h-4 animate-spin mr-1" />
        )}
        <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div 
          ref={dropdownRef}
          className="absolute right-0 mt-2 w-80 bg-white border rounded-lg shadow-lg z-[1000] max-h-96 overflow-y-auto"
        >
          {/* Current Admin Info */}
          <div className="px-4 py-3 border-b bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  currentAdminId ? 'bg-orange-100' : 'bg-indigo-100'
                }`}>
                  {currentAdminId ? (
                    <Users className="w-5 h-5 text-orange-700" />
                  ) : (
                    <User className="w-5 h-5 text-indigo-700" />
                  )}
                </div>
                <div className="flex-2">
                  <div className="font-medium text-gray-900">
                    {currentAdmin ? `${currentAdmin.first_name} ${currentAdmin.last_name}` : 'Loading...'}
                  </div>
                  <div className="text-sm text-gray-500">
                    {currentAdmin?.email}
                  </div>
                  {currentAdminId && (
                    <div className="text-xs text-orange-600 font-medium">
                      Currently Switched
                    </div>
                  )}
                </div>
              </div>

              {/* Help Button */}
              <div className="relative">
                <button
                  onClick={() => setShowHelp(!showHelp)}
                  className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-200"
                  title="How does admin switching work?"
                >
                  <HelpCircle className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Success Message Display */}
          {successMessage && (
            <div className="px-4 py-2 bg-green-50 border-b">
              <div className="flex items-center space-x-2 text-green-700">
                <UserCheck className="w-4 h-4" />
                <span className="text-sm">{successMessage}</span>
              </div>
            </div>
          )}

          {/* Error Display */}
          {(error || loginError) && !successMessage && (
            <div className="px-4 py-2 bg-red-50 border-b">
              <div className="flex items-center space-x-2 text-red-700">
                <AlertCircle className="w-4 h-4" />
                <span className="text-sm">{error || loginError}</span>
              </div>
            </div>
          )}

          {/* Login Form */}
          {showLoginForm ? (
            <div className="p-4 border-b">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-medium text-gray-900">Switch to Admin Account</h3>
                <button
                  onClick={() => setShowLoginForm(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <ArrowLeft className="w-4 h-4" />
                </button>
              </div>
              
              <form onSubmit={handleNewAdminLogin} className="space-y-3">
                <div>
                  <input
                    type="email"
                    placeholder="Admin email"
                    value={loginEmail}
                    onChange={(e) => setLoginEmail(e.target.value)}
                    className="w-full px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    disabled={isLoggingIn}
                    required
                  />
                </div>
                
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Password"
                    value={loginPassword}
                    onChange={(e) => setLoginPassword(e.target.value)}
                    className="w-full px-3 py-2 pr-10 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    disabled={isLoggingIn}
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
                
                <button
                  type="submit"
                  disabled={isLoggingIn || !loginEmail.trim() || !loginPassword.trim()}
                  className="w-full bg-indigo-600 text-white py-2 px-4 rounded-md text-sm font-medium hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                >
                  {isLoggingIn ? (
                    <>
                      <Loader className="w-4 h-4 animate-spin" />
                      <span>Authenticating...</span>
                    </>
                  ) : (
                    <>
                      <LogIn className="w-4 h-4" />
                      <span>Switch Account</span>
                    </>
                  )}
                </button>
              </form>
            </div>
          ) : (
            <>
              {/* Quick Actions */}
              <div className="p-2">
                {/* Return to Primary Admin (if switched) */}
                {currentAdminId && (
                  <button
                    onClick={handleReturnToPrimary}
                    disabled={isAdminSwitching}
                    className="w-full text-left px-3 py-2 hover:bg-gray-100 rounded-md flex items-center space-x-2 text-sm"
                  >
                    <User className="w-4 h-4 text-indigo-600" />
                    <span>Return to Primary Account</span>
                  </button>
                )}

                {/* Add New Admin */}
                <button
                  onClick={() => setShowLoginForm(true)}
                  disabled={isAdminSwitching}
                  className="w-full text-left px-3 py-2 hover:bg-gray-100 rounded-md flex items-center space-x-2 text-sm"
                >
                  <LogIn className="w-4 h-4 text-green-600" />
                  <span>Switch to Another Admin</span>
                </button>
              </div>

              {/* Remembered Admins */}
              {rememberedAdmins.length > 0 && (
                <>
                  <div className="px-4 py-2 border-t border-b bg-gray-50">
                    <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide">
                      Remembered Accounts ({rememberedAdmins.length})
                    </h4>
                  </div>
                  
                  <div className="max-h-48 overflow-y-auto">
                    {rememberedAdmins.map((admin) => (
                      <div
                        key={admin.id}
                        className={`px-3 py-2 hover:bg-gray-50 flex items-center justify-between ${
                          currentAdminId === admin.id ? 'bg-orange-50' : ''
                        }`}
                      >
                        <button
                          onClick={() => handleQuickSwitch(admin.id)}
                          disabled={isAdminSwitching || currentAdminId === admin.id}
                          className={`flex-2 text-left flex items-center space-x-3 ${
                            currentAdminId === admin.id ? 'opacity-60 cursor-not-allowed' : 'hover:bg-gray-100'
                          }`}
                        >
                          <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                            <User className="w-4 h-4 text-gray-600" />
                          </div>
                          <div className="flex-2 min-w-0">
                            <div className="text-sm font-medium text-gray-900 truncate">
                              {admin.first_name} {admin.last_name}
                            </div>
                            <div className="text-xs text-gray-500 truncate">
                              {admin.email}
                            </div>
                            {admin.function && (
                              <div className="text-xs text-gray-400 truncate">
                                {admin.function}
                              </div>
                            )}
                          </div>
                          {currentAdminId === admin.id && (
                            <UserCheck className="w-4 h-4 text-orange-600" />
                          )}
                        </button>
                        
                        <button
                          onClick={(e) => handleRemoveAdmin(admin.id, e)}
                          className={`ml-2 p-1 rounded ${
                            showRemoveConfirm === admin.id
                              ? 'bg-green-100 text-green-700 hover:bg-green-200'
                              : 'text-gray-400 hover:text-red-600 hover:bg-red-100'
                          }`}
                          title={showRemoveConfirm === admin.id ? 'Click to confirm removal' : 'Remove from remembered accounts'}
                        >
                          {showRemoveConfirm === admin.id ? (
                            <Check className="w-4 h-4" />
                          ) : (
                            <Trash2 className="w-4 h-4" />
                          )}
                        </button>
                      </div>
                    ))}
                  </div>
                </>
              )}
            </>
          )}
        </div>
      )}

      {/* Help Tooltip - Rendered outside dropdown to avoid scroll constraints */}
      {showHelp && (
        <div className="absolute right-0 top-full mt-2 w-80 bg-white border rounded-lg shadow-lg p-4 z-[1002]">
          <div className="text-sm text-gray-700">
            <div className="font-medium text-gray-900 mb-3">Admin Account Switching</div>
            <div className="space-y-3">
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <div className="font-medium text-gray-800 mb-1">Limited Scope</div>
                  <p className="text-gray-600">
                    Only <strong>viewing and replying to tickets and tasks</strong> will use the selected admin account.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <div className="font-medium text-gray-800 mb-1">Primary Account Functions</div>
                  <p className="text-gray-600">
                    All other administrative functions (user management, system settings, billing, etc.) continue to use your primary account for security and audit purposes.
                  </p>
                </div>
              </div>
            </div>
          </div>
          <button
            onClick={() => setShowHelp(false)}
            className="absolute top-2 right-2 text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Close help"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}
    </div>
  );
};

export default AdminSwitcher;
